import axios from 'axios';
import { showToast } from 'vant';

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.MODE === 'development' ? '/api' : import.meta.env.VITE_API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    console.log('请求配置:', config);
    
    // 添加 token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.code && res.code !== 200) {
      showToast({
        message: res.message || '请求失败',
        type: 'fail',
        duration: 3000
      });
      
      if (res.code === 401) {
        localStorage.removeItem('token');
      }
      return Promise.reject(new Error(res.message || '请求失败'));
    } else {
      return res;
    }
  },
  error => {
    let message = '请求失败';
    if (error.response) {
      switch (error.response.status) {
        case 400: message = '请求错误'; break;
        case 401: 
          message = '未授权，请登录'; 
          localStorage.removeItem('token');
          break;
        case 403: message = '拒绝访问'; break;
        case 404: message = '请求地址不存在'; break;
        case 500: message = '服务器内部错误'; break;
        default: message = `请求失败: ${error.response.status}`; break;
      }
    } else if (error.request) {
      message = '网络异常，服务器未响应';
    } else {
      message = error.message;
    }

    showToast({
      message,
      type: 'fail',
      duration: 3000,
    });

    return Promise.reject(error);
  }
);

export default service;
