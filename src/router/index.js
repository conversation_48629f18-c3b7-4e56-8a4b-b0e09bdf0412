import { createRouter, createWebHashHistory } from 'vue-router'

// Import views
import Home from '../views/Home.vue'
import Intro from '../views/Intro.vue'
import Honor from '../views/Honor.vue'
import Environment from '../views/Environment.vue'
import Register from '../views/Register.vue'
import EnrollList from '../views/EnrollList.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/intro',
    name: 'Intro',
    component: Intro
  },
  {
    path: '/honor',
    name: 'Honor',
    component: Honor
  },
  {
    path: '/environment',
    name: 'Environment',
    component: Environment
  },
  {
    path: '/register',
    name: 'Register',
    component: Register
  },
  {
    path: '/enroll-list',
    name: 'EnrollList',
    component: EnrollList
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
