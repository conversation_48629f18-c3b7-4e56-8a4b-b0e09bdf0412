import request from '../utils/request';

// 通用请求方法 - 标准的 GitHub 上常见的封装方式
export const http = {
  get(url, params) {
    return request({
      url,
      method: 'get',
      params
    });
  },
  post(url, data) {
    return request({
      url,
      method: 'post',
      data
    });
  },
  put(url, data) {
    return request({
      url,
      method: 'put',
      data
    });
  },
  delete(url, params) {
    return request({
      url,
      method: 'delete',
      params
    });
  }
};

export default http;
