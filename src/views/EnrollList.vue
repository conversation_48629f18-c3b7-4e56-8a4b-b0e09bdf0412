<template>
  <div class="enroll-list">
    <van-nav-bar title="报名信息" fixed placeholder />
    
    <van-cell-group inset class="card-group">
      <van-cell
        v-for="item in tableData"
        :key="item.VId"
        :title="`${item.VId}. ${item.VBabyname}`">
        <template #label>
          <div>性别: {{formatSex(item.VSex)}}</div>
          <div>出生: {{item.vDate}}</div>
          <div>家长: {{item.VName}}</div>
          <div>电话:<a href="tel:{{item.VPhone}}">{{item.VPhone}}</a></div>
          <div v-if="item.VNote">备注: {{item.VNote}}</div>
          <div>留言时间：{{ transTime(item.vDateTime) }}</div>
        </template>
      </van-cell>
    </van-cell-group>
    <van-pagination
      v-model="currentPage"
      :page-count="totalPages"
      mode="simple"
      @change="fetchData"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { enrollApi } from '../api/modules'
import {
  Cell as VanCell,
  CellGroup as VanCellGroup,
  Pagination as VanPagination,
  Icon as VanIcon
} from 'vant'

const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(30)
const totalPages = ref(1)

const fetchData = async () => {
  try {
    const res = await enrollApi.getEnroll({
      keyName: '111',
      pageIndex: currentPage.value
    })
    tableData.value = res.Data
    totalPages.value = parseInt(res.Message)
  } catch (error) {
    console.error(error)
  }
}

const formatSex = (sex) => {
  return sex === 0 ? '女' : '男'
}

const transTime = (date) => {
  const d = new Date(date);
  return d.toLocaleString("zh-CN", {
    year: "numeric",
    month: "numeric",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false
  });
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped lang="scss">
.enroll-list {
  padding: 12px;
  background-color: #f7f8fa;
  min-height: 100vh;
  
  .van-cell {
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    position: relative;
    
    &::after {
      content: "";
      position: absolute;
      left: 16px;
      right: 16px;
      bottom: -8px;
      height: 1px;
      background-color: #f0f0f0;
    }
    
    ::v-deep .van-cell__label {
      font-size: 14px;
      line-height: 1.8em;
      color: #666;
      
      div {
        margin-bottom: 6px;
      }
    }
    
    &__title {
      font-weight: 600;
      color: #1a1a1a;
      font-size: 16px;
    }
    
    &__value {
      font-size: 14px;
      color: #1989fa;
    }
  }
  
  .van-pagination {
    margin: 16px 0;
    justify-content: center;
  }
}
</style>