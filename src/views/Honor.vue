<template>
  <div class="honor-container">
    <div class="honor-header animate-entry fade-down">
      <h2 class="page-title">园所荣誉</h2>
      <div class="title-decoration">
        <span class="line"></span>
        <span class="dot"></span>
        <span class="line"></span>
      </div>
    </div>
    <!-- TODO -->
    <div class="honor-content">
      <p>载誉前行，硕果累累</p>
      <p>启稚幼儿园的荣誉之路，记录着每一步坚实的成长：</p>
      <ul>
        <li>2017年 荣获“九龙坡区幼儿园综合目标考核一等奖”</li>
        <li>2017年 荣获 “九龙坡区卫生示范学校”称号</li>
        <li>2017年 荣获 “九龙坡区卫生示范食堂”称号</li>
        <li>2017年 市红领巾志愿者协会授予“爱心幼儿园”的称号</li>
        <li>2018年 荣获“九龙坡区一级幼儿园”</li>
        <li>2018年 荣获“九龙坡区幼儿园综合目标考核一等奖”</li>
        <li>2019年 荣获“九龙坡区幼儿园综合目标考核一等奖”</li>
        <li>2020年 荣获“九龙坡区幼儿园综合目标考核一等奖”</li>
        <li>2021年 荣获“九龙坡区幼儿园综合目标考核一等奖”</li>
        <li>2021年 荣获“九龙坡区平安校园”称号</li>
        <li>2021年 荣获“奔跑吧·少年重庆市幼儿篮球嘉年华特等奖”</li>
        <li>2022年 荣获“九龙坡区幼儿园保育教育质量评价考核一等奖”</li>
        <li>2023年 荣获“九龙坡区学校（幼儿园）办学质量评价一等奖”</li>
        <li>2024年 荣获“九龙坡区学校办学质量评价一等奖”</li>
        <li>2024年 荣获“奔跑吧·少年重庆市第三届体育大会幼儿体适能活动二等奖</li>
        <li>2025年 荣获“重庆市九龙坡区一级托育机构”</li>
      </ul>
      <div class="honor-gallery">
        <div class="gallery-grid">
          <div class="honor-card animate-scroll"
               v-for="(img, index) in honorImages"
               :key="index">
            <img :src="img.src" :alt="img.title" class="honor-image" />
            <div class="honor-info">
              <h3 class="honor-title">{{ img.title }}</h3>
              <p class="honor-year">{{ img.year }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- /TODO -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const activeCard = ref(null);

onMounted(() => {
  // 页面加载时的初始动画
  const animateElements = document.querySelectorAll('.animate-entry');
  animateElements.forEach((el, index) => {
    setTimeout(() => {
      el.classList.add('visible');
    }, 100 * index);
  });

  // 为滚动元素添加交叉观察器
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        // 对于已经显示的元素，可以停止观察
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '0px 0px -100px 0px'
  });
  
  const scrollElements = document.querySelectorAll('.animate-scroll');
  scrollElements.forEach(el => {
    observer.observe(el);
  });
});
const honorImages = ref([
  { src: '/imgs/honor/2017.jpg', title: '办园综合目标考核', year: '2017' },
  { src: '/imgs/honor/2018.jpg', title: '办园综合目标考核', year: '2018' },
  { src: '/imgs/honor/2019.jpg', title: '办园综合目标考核', year: '2019' },
  { src: '/imgs/honor/2020.jpg', title: '办园综合目标考核', year: '2020' },
  { src: '/imgs/honor/2021.jpg', title: '办园综合目标考核', year: '2021' },
  { src: '/imgs/honor/2022.jpg', title: '办园综合目标考核', year: '2022' },
  { src: '/imgs/honor/2023.jpg', title: '办园综合目标考核', year: '2023' },
  { src: '/imgs/honor/2024.jpg', title: '办园综合目标考核', year: '2024' },
  { src: '/imgs/honor/benpao.jpg', title: '奔跑吧少年', year: '2024' },
  { src: '/imgs/honor/lanqiu1.jpg', title: '篮球对抗', year: '2023' },
  { src: '/imgs/honor/lanqiu2.jpg', title: '篮球嘉年华', year: '2021' },
  { src: '/imgs/honor/shipin2.jpg', title: '食品安全食堂', year: '2018' },
  { src: '/imgs/honor/shipin1.jpg', title: '食品安全', year: '2017' },
  { src: '/imgs/honor/2017-10.jpg', title: '卫生示范学校', year: '2017' },
  { src: '/imgs/honor/2017-11.jpg', title: '一级幼儿园' },
  { src: '/imgs/honor/2017-12.jpg', title: 'A级厨房' },
]);

</script>

<style scoped>
.honor-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  color: #333;
  line-height: 1.6;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Microsoft YaHei', sans-serif;
  overflow: hidden;
}

.honor-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 12px;
  letter-spacing: 1px;
  position: relative;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #409EFF, transparent);
  animation: shimmer 3s infinite linear;
  background-size: 200% 100%;
}

.title-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0 20px;
}

.line {
  height: 1px;
  width: 40px;
  background-color: #409EFF;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #409EFF;
  margin: 0 8px;
  animation: pulse 2s infinite;
}

.honor-content {
  padding: 10px 0;
}

.honor-description {
  text-align: center;
  margin-bottom: 25px;
  font-size: 16px;
  color: #666;
  padding: 0 15px;
  line-height: 1.8;
}

.honor-image-wrapper {
  margin: 30px 0;
  position: relative;
  perspective: 1000px;
}

.honor-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.honor-image-container:hover {
  transform: rotateY(5deg);
}

.honor-badge {
  position: absolute;
  top: -15px;
  right: 20px;
  background: linear-gradient(135deg, #409EFF, #2c8af8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  z-index: 2;
  transform: translateZ(20px);
  transition: all 0.4s ease;
}

.honor-image-container:hover .honor-badge {
  transform: translateZ(30px) translateY(-5px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);
}

.honor-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
  transform: translateZ(0);
}

.honor-image-container:hover .honor-image {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  transform: translateZ(10px);
}

.image-caption {
  margin-top: 15px;
  font-size: 14px;
  color: #888;
  text-align: center;
  transform: translateZ(5px);
  transition: transform 0.4s ease;
}

.honor-image-container:hover .image-caption {
  transform: translateZ(15px);
  color: #409EFF;
}

.honor-footer {
  margin-top: 30px;
  text-align: center;
  padding: 20px 0;
  border-top: 1px dashed #eaeaea;
}

.footer-text {
  font-size: 15px;
  color: #409EFF;
  font-style: italic;
  position: relative;
  display: inline-block;
}

.footer-text::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: #409EFF;
  transition: width 0.6s ease;
}

.honor-footer:hover .footer-text::after {
  width: 100%;
}

/* 动画类 */
.animate-entry, .animate-scroll {
  opacity: 0;
  transform-origin: center;
  transition: opacity 0.8s cubic-bezier(0.215, 0.61, 0.355, 1),
              transform 0.8s cubic-bezier(0.215, 0.61, 0.355, 1);
  will-change: opacity, transform;
  transition-delay: var(--delay, 0s);
}

.animate-entry.visible, .animate-scroll.visible {
  opacity: 1;
  transform: translate3d(0, 0, 0) scale(1) rotate(0deg) !important;
}

.fade-up {
  transform: translate3d(0, 40px, 0);
}

.fade-down {
  transform: translate3d(0, -40px, 0);
}

.scale-in {
  transform: scale(0.8);
}

/* 动画关键帧 */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@media (max-width: 768px) {
  .honor-container {
    padding: 15px;
  }
  
  .page-title {
    font-size: 22px;
  }
  
  .honor-description {
    font-size: 15px;
  }
  
  .honor-badge {
    right: 10px;
    padding: 6px 12px;
    font-size: 12px;
  }
}
.honor-gallery {
  margin: 30px 0;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
}

.honor-card {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.honor-image {
  width: 100%;
  height: auto;
  display: block;
}

.honor-info {
  padding: 8px;
  background: #f8f8f8;
}

.honor-title {
  margin: 0;
  font-size: 14px;
  color: #333;
}

.honor-year {
  margin: 4px 0 0;
  font-size: 12px;
  color: #666;
}

.honor-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.honor-year {
  margin: 5px 0 0;
  font-size: 14px;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }
  
  .card-overlay {
    padding: 15px;
  }
  
  .honor-title {
    font-size: 16px;
  }
  
  .honor-year {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}
</style>
