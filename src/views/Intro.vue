<template>
  <div class="intro-container">
    <!-- 动态背景 -->
    <div class="dynamic-background">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>

    <!-- 主标题区域 -->
    <div class="hero-section animate-entry fade-down">
      <div class="hero-content">
        <h1 class="hero-title">
          <span class="title-main">启稚幼儿园</span>
          <span class="title-sub">用爱点亮每个孩子的未来</span>
        </h1>
        <div class="hero-decoration">
          <div class="decoration-line"></div>
          <div class="decoration-circle">
            <Icon name="star" color="#FFD700" size="24" />
          </div>
          <div class="decoration-line"></div>
        </div>
        <p class="hero-description">
          专业蒙台梭利教育 · 国际化双语环境 · 温馨成长乐园
        </p>
      </div>
    </div>

    <!-- 园所概况 -->
    <div class="about-section animate-entry fade-up">
      <div class="section-container">
        <div class="about-image">
          <div class="image-frame">
            <img
              src="/imgs/intro/01.jpg"
              alt="启稚幼儿园外观"
              class="main-image"
              loading="lazy"
            />
            <div class="image-overlay">
              <div class="overlay-content">
                <Icon name="home-o" color="#fff" size="32" />
                <span>温馨校园</span>
              </div>
            </div>
          </div>
          <!-- 装饰元素 -->
          <div class="decorative-elements">
            <div class="element element-1"></div>
            <div class="element element-2"></div>
            <div class="element element-3"></div>
          </div>
        </div>
        <div class="about-content">
          <div class="content-header">
            <span class="section-tag">About Us</span>
            <h2 class="section-title">关于启稚幼儿园</h2>
            <div class="title-underline"></div>
          </div>
          <div class="content-text">
            <div class="highlight-box">
              <Icon name="award-o" color="#FF6B6B" size="24" />
              <span>重庆市一级幼儿园 · 连续8年一等奖</span>
            </div>
            <p class="text-paragraph">
              启稚幼儿园创办于2016年，是一所具备高品质、国际化、设备先进、教学领先的蒙台梭利双语幼儿园。我们致力于为每个孩子提供最优质的早期教育环境。
            </p>
            <p class="text-paragraph">
              2019年战略升级，拓展早期教育领域成立启稚托育园，形成了0-6岁成长衔接体系，构筑品牌化、一体化互动滋养的早期教育生态链。
            </p>
            <div class="achievement-stats">
              <div class="stat-item">
                <div class="stat-number">2016</div>
                <div class="stat-label">创办年份</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">8+</div>
                <div class="stat-label">年教学经验</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">0-6</div>
                <div class="stat-label">岁教育体系</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 蒙台梭利教育体系 -->
    <div class="education-system animate-scroll fade-up">
      <div class="section-container">
        <div class="section-header">
          <span class="section-tag">Education System</span>
          <h2 class="section-title">蒙台梭利教育纵深发展体系</h2>
          <div class="title-underline"></div>
          <p class="section-description">
            基于"早期教育生态链"理论框架，启稚采用多维度一体化成长模型
          </p>
        </div>

        <div class="education-timeline">
          <div class="timeline-line"></div>

          <div class="stage-card animate-scroll slide-up" style="--delay: 0s">
            <div class="stage-number">01</div>
            <div class="stage-content">
              <div class="stage-header">
                <div class="age-badge age-early">6个月-2岁</div>
                <h3 class="stage-title">早期教育阶段</h3>
                <div class="stage-icon">
                  <Icon name="smile-o" color="#FF6B6B" size="28" />
                </div>
              </div>
              <div class="stage-description">
                <p>以蒙氏理论为依托，针对婴幼儿开展的综合性教育活动，通过科学系统的引导促进儿童在心理、智力、运动、情感、社交等多维度的全面发展。</p>
                <div class="stage-features">
                  <span class="feature-tag">心理发展</span>
                  <span class="feature-tag">智力启蒙</span>
                  <span class="feature-tag">运动协调</span>
                  <span class="feature-tag">情感培养</span>
                </div>
              </div>
              <div class="stage-gallery">
                <div class="gallery-grid">
                  <div class="gallery-item" v-for="(img, index) in ['02.jpg', '03.jpg']" :key="index">
                    <img :src="`/imgs/intro/${img}`" :alt="`早期教育${index + 1}`" />
                    <div class="gallery-overlay">
                      <Icon name="eye-o" color="#fff" size="20" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="stage-card animate-scroll slide-up" style="--delay: 0.2s">
            <div class="stage-number">02</div>
            <div class="stage-content">
              <div class="stage-header">
                <div class="age-badge age-toddler">2-3岁</div>
                <h3 class="stage-title">托育阶段</h3>
                <div class="stage-icon">
                  <Icon name="friends-o" color="#4ECDC4" size="28" />
                </div>
              </div>
              <div class="stage-description">
                <p>采用蒙台梭利婴幼儿IC标准，配备五感发展课程，在早期教育阶段全面发展的基础上，着重培育幼儿自主意识与基础生存能力。</p>
                <div class="stage-features">
                  <span class="feature-tag">五感发展</span>
                  <span class="feature-tag">自主意识</span>
                  <span class="feature-tag">生存能力</span>
                  <span class="feature-tag">社交启蒙</span>
                </div>
              </div>
              <div class="stage-gallery">
                <div class="gallery-grid">
                  <div class="gallery-item" v-for="(img, index) in ['04.jpg', '05.jpg', '06.jpg', '07.jpg']" :key="index">
                    <img :src="`/imgs/intro/${img}`" :alt="`托育阶段${index + 1}`" />
                    <div class="gallery-overlay">
                      <Icon name="eye-o" color="#fff" size="20" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="stage-card animate-scroll slide-up" style="--delay: 0.4s">
            <div class="stage-number">03</div>
            <div class="stage-content">
              <div class="stage-header">
                <div class="age-badge age-kindergarten">3-6岁</div>
                <h3 class="stage-title">幼儿园阶段</h3>
                <div class="stage-icon">
                  <Icon name="star-o" color="#FFD93D" size="28" />
                </div>
              </div>
              <div class="stage-description">
                <p>以蒙台梭利五大领域为根基，同步结合半日浸入式双语环境（中英教学1:1），深度融合幼小衔接培养体系。赋能幼儿从入园到小学的无缝衔接。</p>
                <div class="stage-features">
                  <span class="feature-tag">五大领域</span>
                  <span class="feature-tag">双语环境</span>
                  <span class="feature-tag">幼小衔接</span>
                  <span class="feature-tag">国际视野</span>
                </div>
              </div>
              <div class="stage-gallery">
                <div class="gallery-grid">
                  <div class="gallery-item" v-for="(img, index) in ['11.jpg', '12.jpg', '13.jpg', '14.jpg']" :key="index">
                    <img :src="`/imgs/intro/${img}`" :alt="`幼儿园阶段${index + 1}`" />
                    <div class="gallery-overlay">
                      <Icon name="eye-o" color="#fff" size="20" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 教学特色 -->
    <div class="features-section animate-scroll fade-up">
      <div class="section-container">
        <div class="section-header">
          <span class="section-tag">Teaching Features</span>
          <h2 class="section-title">教学特色</h2>
          <div class="title-underline"></div>
          <p class="section-description">
            多元化教学模式，全方位培养孩子的综合能力
          </p>
        </div>

        <div class="features-grid">
          <div
            v-for="(feature, index) in features"
            :key="index"
            class="feature-card animate-scroll slide-up"
            :style="`--delay: ${index * 0.1}s`"
          >
            <div class="feature-image">
              <img :src="`/imgs/intro/${feature.image}`" :alt="feature.title" />
              <div class="image-overlay">
                <div class="overlay-icon">
                  <Icon :name="feature.icon" color="#fff" size="32" />
                </div>
              </div>
            </div>
            <div class="feature-content">
              <div class="feature-header">
                <div class="feature-icon">
                  <Icon :name="feature.icon" :color="feature.color" size="24" />
                </div>
                <h3 class="feature-title">{{ feature.title }}</h3>
              </div>
              <p class="feature-description">{{ feature.description }}</p>
              <div class="feature-highlights">
                <span
                  v-for="highlight in feature.highlights"
                  :key="highlight"
                  class="highlight-tag"
                >
                  {{ highlight }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 师资培育系统 -->
    <!-- 师资培育体系 -->
    <div class="teacher-section section-block animate-scroll fade-up">
      <h3 class="section-title">师资培育体系</h3>
      <div class="title-decoration small">
        <span class="line"></span>
        <span class="dot rotate-on-scroll"></span>
        <span class="line"></span>
      </div>
      <p class="section-intro">作为CMS官方教师培训基地，构建AMS持证教师+CMS认证中教+专业的英文教学团队，教龄5年以上骨干教师占比达70%。</p>
      
      <div class="teacher-stats">
        <div class="stat-card animate-scroll slide-left" style="--delay: 0s">
          <div class="stat-value">100%</div>
          <div class="stat-label">持证上岗</div>
        </div>
        <div class="stat-card animate-scroll slide-left" style="--delay: 0.2s">
          <div class="stat-value">70%</div>
          <div class="stat-label">5年以上教龄</div>
        </div>
        <div class="stat-card animate-scroll slide-left" style="--delay: 0.4s">
          <div class="stat-value">3:1</div>
          <div class="stat-label">师生比例</div>
        </div>
      </div>
    </div>
    
    <!-- 教育空间赋能体系 -->
    <div class="space-section section-block animate-scroll fade-up">
      <h3 class="section-title">教育空间赋能体系</h3>
      <div class="title-decoration small">
        <span class="line"></span>
        <span class="dot rotate-on-scroll"></span>
        <span class="line"></span>
      </div>
      <p class="section-intro">园所以蒙台梭利"有准备的环境"理论为指导，打造4000㎡智慧教学综合体</p>
      
      <div class="space-categories">
        <div class="space-category animate-scroll slide-left" style="--delay: 0s">
          <h4 class="category-title">
            <Icon name="home-o" color="#409EFF" />
            主题学习场地
          </h4>
          <ul class="space-list">
            <li>蒙台梭利活动室</li>
            <li>安科纳语言馆</li>
            <li>科学实验馆</li>
            <li>绘本阅读馆</li>
          </ul>
        </div>
        
        <div class="space-category animate-scroll slide-left" style="--delay: 0.2s">
          <h4 class="category-title">
            <Icon name="flower-o" color="#409EFF" />
            素质发展空间
          </h4>
          <ul class="space-list">
            <li>多功能运动馆</li>
            <li>创意美劳馆</li>
            <li>厨艺吧</li>
            <li>多功能表演厅</li>
          </ul>
        </div>
        
        <div class="space-category animate-scroll slide-left" style="--delay: 0.4s">
          <h4 class="category-title">
            <Icon name="environment-o" color="#409EFF" />
            自然探索生态
          </h4>
          <ul class="space-list">
            <li>2000㎡户外运动区</li>
            <li>体适能训练动线</li>
            <li>足球场、篮球场</li>
            <li>攀岩区、生态种植园</li>
          </ul>
        </div>
      </div>

      <van-swipe class="gallery" :loop="true" :autoplay="3000" v-if="imagesLoaded" @change="handleSwipeChange">
        <van-swipe-item v-for="(img, index) in images" :key="index">
          <img :src="img" @load="onImageLoad" />
        </van-swipe-item>
      </van-swipe>
    </div>
    
    <!-- 教育使命与理念 -->
    <div class="mission-section section-block animate-scroll fade-up">
      <h3 class="section-title">启稚文化</h3>
      <div class="title-decoration small">
        <span class="line"></span>
        <span class="dot"></span>
        <span class="line"></span>
      </div>
      
      <div class="mission-cards">
        <div class="mission-card animate-scroll flip-in" style="--delay: 0s">
          <h4 class="mission-title">
            <Icon name="aim" color="#409EFF" />
            教育使命
          </h4>
          <p class="mission-content">
            致力于您的孩子成就非凡,守护幼儿与生俱来的探索热忱，赋能终身自主成长力！
          </p>
        </div>
        
        <div class="mission-card animate-scroll flip-in" style="--delay: 0.2s">
          <h4 class="mission-title">
            <Icon name="bulb-o" color="#409EFF" />
            办园理念
          </h4>
          <p class="mission-content">
            “以爱为本、用心教育”以蒙台梭利为根基，让教育回归本质。
          </p>
        </div>
        
        <div class="mission-card animate-scroll flip-in" style="--delay: 0.4s">
          <h4 class="mission-title">
            <Icon name="flag-o" color="#409EFF" />
            育人目标
          </h4>
          <p class="mission-content">
            培育温暖世界的成长小使者<br />
            我们致力于点燃孩子与生俱来的好奇心与生命力，在爱与探索中滋养身心，在多元互动中感知世界，培育温暖世界的成长小使者。
          </p>
        </div>
      </div>
    </div>
    <!-- 联系我们 -->
    <div class="contact-section section-block animate-scroll fade-up">
      <h3 class="section-title">联系我们</h3>
      <div class="title-decoration small">
        <span class="line"></span>
        <span class="dot rotate-on-scroll"></span>
        <span class="line"></span>
      </div>
      
      <div class="contact-content">
        <div class="contact-card animate-scroll slide-left" style="--delay: 0s">
          <div class="contact-image">
            <img src="/imgs/intro/code.jpg" />
          </div>
          <div class="contact-info">
            <p class="contact-text">扫码添加咨询</p>
            <p class="contact-text">咨询更多信息</p>
            <p class="contact-phone">
              <Icon name="phone-o" color="#409EFF" />
              <a href="tel:17782366210" style="color: #409EFF;">17782366210</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Icon, Swipe, SwipeItem } from 'vant';
import { ref, onMounted, onUnmounted } from 'vue';

const features = ref([
  {
    title: '蒙台梭利教育',
    description: '园所采用蒙台梭利教育理念为主要课程体系，结合《3-6岁儿童发展指南》，全面有序培育热爱生活、热爱自然、热爱自己的"三爱"儿童。用"有准备的环境+有察觉的引导"，唤醒幼儿内在生长力，尊重儿童成长步调，帮助儿童自然成长，获得身体、意志独立。',
    image: '15.jpg'
  },
  {
    title: '半日浸入式英文',
    description: '围绕日常生活和实景应用，打造英文深度交融的沉浸式场景，让幼儿理解多元文化，形成国际视野。在工作、学习、游戏等真实体验中触发自然表达。',
    image: '16.jpg'
  },
  {
    title: '户外阳光体能',
    description: '通过结合各种器材和游戏，增强幼儿的身体适应能力和运动健康，激发幼儿的潜能和灵活思维，培养幼儿的观察、思考和解决问题的能力，提升智力水平。',
    image: '17.jpg'
  },
  {
    title: '多元化课程',
    description: '以蒙台梭利教育理念为核心，尊重幼儿成长步调，构建"科学、生活、艺术、文化四位一体的多元化课程体系"。在这里，我们提倡"全人教育"，科学课培养思辨力，食育课修炼生活家，艺术课激活创造力，文化课孕育包容心。让孩子在自主探索中感知世界，在多元碰撞中构建思维，成长为有好奇心，行动力与世界视野的小公民。',
    image: '18.jpg'
  },
  {
    title: '多元早期亲子教育',
    description: '秉承蒙台梭利教育理念，以"有准备的环境"为核心，针对婴幼儿敏感期，专为6个月-3岁婴幼儿及家庭打造"多元早期亲子课"，以科学、温暖的方式开启孩子的成长黄金期，构建亲子共育的桥梁。课程融合蒙氏教育精髓与多元感官探索，为婴幼儿提供全面发展的启蒙空间，同时赋能家长，成为孩子成长的第一位"导师"。',
    image: '19.jpg'
  }
]);

const images = ref([
  '/imgs/intro/20.jpg',
  '/imgs/intro/21.jpg',
  '/imgs/intro/22.jpg',
  '/imgs/intro/23.jpg'
]);
const imagesLoaded = ref(false);
const loadedImagesCount = ref(0);

const onImageLoad = () => {
  loadedImagesCount.value++;
  if (loadedImagesCount.value === images.value.length) {
    imagesLoaded.value = true;
    console.log('All images loaded, swipe autoplay enabled');
  }
};

onMounted(() => {
  // 页面加载时的初始动画
  const animateElements = document.querySelectorAll('.animate-entry');
  animateElements.forEach((el, index) => {
    setTimeout(() => {
      el.classList.add('visible');
    }, 100 * index);
  });

  // 为滚动元素添加交叉观察器
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
        // 对于已经显示的元素，可以停止观察
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1,
    rootMargin: '0px 0px -100px 0px'
  });
  
  const scrollElements = document.querySelectorAll('.animate-scroll');
  scrollElements.forEach(el => {
    observer.observe(el);
  });

  // 添加视差滚动效果
  window.addEventListener('scroll', handleParallax);
  
  // 初始化视差效果
  handleParallax();
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleParallax);
});

// 视差滚动效果处理函数
const handleParallax = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  
  // 视差背景效果
  const parallaxBgs = document.querySelectorAll('.parallax-bg');
  parallaxBgs.forEach(bg => {
    const speed = parseFloat(bg.getAttribute('data-speed')) || 0.2;
    const yPos = -(scrollTop * speed);
    bg.style.transform = `translate3d(0, ${yPos}px, 0)`;
  });
  
  // 视差元素效果
  const parallaxElements = document.querySelectorAll('.parallax-element');
  parallaxElements.forEach(el => {
    const speed = parseFloat(el.getAttribute('data-speed')) || 0.1;
    const yPos = -(scrollTop * speed);
    el.style.transform = `translate3d(0, ${yPos}px, 0)`;
  });
  
  // 图片缩放效果
  const scaleElements = document.querySelectorAll('.scale-on-scroll');
  scaleElements.forEach(el => {
    const rect = el.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    
    if (rect.top < windowHeight && rect.bottom > 0) {
      const scrollPercentage = (windowHeight - rect.top) / (windowHeight + rect.height);
      const scale = 1 + Math.min(scrollPercentage * 0.2, 0.2);
      el.style.transform = `scale(${scale})`;
    }
  });
  
  // 旋转效果
  const rotateElements = document.querySelectorAll('.rotate-on-scroll');
  rotateElements.forEach(el => {
    const rect = el.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    
    if (rect.top < windowHeight && rect.bottom > 0) {
      const scrollPercentage = (windowHeight - rect.top) / (windowHeight + rect.height);
      const rotate = scrollPercentage * 10; // 最大旋转10度
      el.style.transform = `rotate(${rotate}deg)`;
    }
  });
};

const handleSwipeChange = (index) => {
  console.log(`Swipe changed to index: ${index}`);
};
</script>

<style scoped>
* img {
  width: 100%;
}

.gallery {
  margin-top: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.van-swipe-item {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
/* 保留原有样式 */
.intro-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0;
  color: #333;
  line-height: 1.6;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Microsoft YaHei', sans-serif;
  position: relative;
  overflow: hidden;
}

/* 视差背景 */
.parallax-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  will-change: transform;
}

.main-bg {
  background: radial-gradient(circle at 50% 50%, rgba(64, 158, 255, 0.05) 0%, transparent 70%);
  opacity: 0.8;
}

.section-bg {
  background: radial-gradient(circle at 50% 50%, rgba(64, 158, 255, 0.03) 0%, transparent 70%);
  opacity: 0.5;
}

/* 动画基础类 */
.animate-entry, .animate-scroll {
  opacity: 0;
  transform-origin: center;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  will-change: opacity, transform, filter;
  filter: blur(2px);
}

.animate-entry.visible, .animate-scroll.visible {
  opacity: 1;
  transform: translate3d(0, 0, 0) scale(1) rotate(0deg) !important;
  filter: blur(0);
}

/* 延迟动画 */
.animate-entry, .animate-scroll {
  transition-delay: var(--delay, 0s);
}

/* 各种动画效果 */
.fade-up {
  transform: translate3d(0, 40px, 0);
}

.fade-down {
  transform: translate3d(0, -40px, 0);
}

.slide-left {
  transform: translate3d(-60px, 0, 0);
}

.slide-right {
  transform: translate3d(60px, 0, 0);
}

.zoom-in {
  transform: scale(0.8);
}

.scale-in {
  transform: scaleX(0);
}

.flip-in {
  transform: perspective(1000px) rotateY(10deg) translateZ(20px);
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* 视差元素 */
.parallax-element {
  will-change: transform;
  overflow: hidden;
}

/* 滚动缩放元素 */
.scale-on-scroll {
  transition: transform 0.5s ease-out;
  will-change: transform;
}

/* 滚动旋转元素 */
.rotate-on-scroll {
  transition: transform 0.5s ease-out;
  will-change: transform;
}

/* 师资培育系统 - 全新设计 */
.teachers-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.teacher-card {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.teacher-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(64, 158, 255, 0.15);
}

.teacher-image {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.teacher-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: transform 0.7s ease;
}

.teacher-card:hover .teacher-image img {
  transform: scale(1.08);
}

/* 新的徽章设计 */
.teacher-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background-color: #409EFF;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.teacher-card:hover .teacher-badge {
  background-color: #2c8af8;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.teacher-desc {
  padding: 1.5rem;
  background-color: #fff;
}

.teacher-desc p {
  margin: 0;
  line-height: 1.7;
  color: #555;
}

/* 新增动画效果 */
.slide-up-fade {
  transform: translateY(30px);
  opacity: 0;
}

.slide-up-fade.visible {
  transform: translateY(0);
  opacity: 1;
}

@media (min-width: 768px) {
  .teacher-card {
    display: flex;
    flex-direction: row;
    align-items: stretch;
  }
  
  .teacher-image {
    width: 40%;
    height: auto;
  }
  
  .teacher-desc {
    width: 60%;
    display: flex;
    align-items: center;
  }
}

/* 原有样式保留 */
.intro-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 12px;
  letter-spacing: 1px;
}

.title-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0 20px;
}

.title-decoration.small {
  margin: 5px auto 20px;
}

.line {
  height: 1px;
  width: 40px;
  background-color: #409EFF;
}

.title-decoration.small .line {
  width: 30px;
}

.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #409EFF;
  margin: 0 8px;
}

.title-decoration.small .dot {
  width: 4px;
  height: 4px;
}

.section-block {
  margin-bottom: 2rem;
  background-color: #fff;
  border-radius: 12px;
  padding: 1.8rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(64, 158, 255, 0.1);
}

.section-block:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.15);
  border-color: rgba(64, 158, 255, 0.3);
}

/* 添加发光边框效果 */
.section-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  background: linear-gradient(45deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  background-size: 200% 200%;
  animation: borderGlow 3s linear infinite;
  z-index: 0;
}

@keyframes borderGlow {
  0% { background-position: 0% 0%; }
  50% { background-position: 100% 100%; }
  100% { background-position: 0% 0%; }
}

.section-title {
  text-align: center;
  color: #409EFF;
  margin-bottom: 0.8rem;
  font-size: 20px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.section-subtitle {
  color: #409EFF;
  margin-bottom: 0.8rem;
  font-size: 18px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.text-divider {
  width: 40px;
  height: 2px;
  background-color: #409EFF;
  margin: 10px 0 15px;
  position: relative;
  z-index: 1;
}

.section-intro {
  text-align: center;
  margin-bottom: 1.8rem;
  color: #666;
  line-height: 1.7;
  position: relative;
  z-index: 1;
}

/* 园所概况 */
.content-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-bottom: 3rem;
  background-color: #fff;
  border-radius: 12px;
  padding: 1.8rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.content-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(64, 158, 255, 0.03) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.content-section:hover::after {
  opacity: 1;
}

.image-placeholder {
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.image-placeholder img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.image-placeholder:hover img {
  transform: scale(1.03);
}

.text-content {
  line-height: 1.7;
  position: relative;
  z-index: 1;
}

.text-content p {
  margin-bottom: 1rem;
  color: #555;
}

/* 蒙台梭利教育体系 */
.education-stages {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

.stage-card {
  background-color: #f8f9fa;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.04);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.stage-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

/* 添加卡片悬停时的光效 */
.stage-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 60%);
  opacity: 0;
  transform: scale(0.5);
  transition: transform 0.5s ease, opacity 0.5s ease;
  pointer-events: none;
}

.stage-card:hover::after {
  opacity: 0.2;
  transform: scale(1);
}

.stage-header {
  background-color: #e6f1ff;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.stage-age {
  background-color: #409EFF;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(64, 158, 255, 0.3);
  position: relative;
  overflow: hidden;
}

/* 添加年龄标签的波纹效果 */
.stage-age::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 150%;
  height: 150%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 60%);
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  transition: transform 0.5s ease, opacity 0.5s ease;
  pointer-events: none;
}

.stage-card:hover .stage-age::after {
  transform: translate(-50%, -50%) scale(1);
  opacity: 0.3;
}

.stage-title {
  margin: 0;
  color: #333;
  font-weight: 600;
}

.stage-content {
  padding: 1.2rem;
  position: relative;
  z-index: 1;
}

.stage-content p {
  margin: 0;
  line-height: 1.7;
  color: #555;
}

/* 师资培育系统 */
.teachers-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

.teacher-image {
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.teacher-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.teacher-image:hover img {
  transform: scale(1.03);
}

.teacher-desc {
  position: relative;
  z-index: 1;
}

.teacher-desc p {
  color: #555;
  line-height: 1.7;
}

/* 教育空间赋能系统 */
.space-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.space-category {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 1.2rem;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.04);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.space-category:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.category-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #409EFF;
  margin-bottom: 1rem;
  font-size: 16px;
  font-weight: 600;
}

.space-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.space-list li {
  padding: 0.5rem 0;
  border-bottom: 1px dashed #e0e0e0;
  color: #555;
  position: relative;
  padding-left: 1.2rem;
}

.space-list li:last-child {
  border-bottom: none;
}

.space-list li::before {
  content: '•';
  color: #409EFF;
  position: absolute;
  left: 0;
  top: 0.5rem;
}

.space-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  position: relative;
  z-index: 1;
}

.gallery-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.gallery-item:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.gallery-item img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
  padding: 0.8rem;
  font-size: 0.9rem;
  text-align: center;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-caption {
  transform: translateY(0);
}

/* 教育使命与理念 */
.mission-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

.mission-card {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 1.5rem;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.04);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mission-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

/* 添加卡片悬停时的光效 */
.mission-card::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 300%;
  height: 300%;
  background: radial-gradient(circle, rgba(64, 158, 255, 0.2) 0%, transparent 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: transform 0.5s ease, opacity 0.5s ease;
  pointer-events: none;
}

.mission-card:hover::before {
  opacity: 1;
  transform: scale(1);
}

.mission-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #409EFF;
  margin-bottom: 1rem;
  font-size: 16px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.mission-content {
  color: #555;
  line-height: 1.7;
  position: relative;
  z-index: 1;
}

/* 响应式调整 */
@media (min-width: 992px) {
  .content-section {
    flex-direction: row;
    align-items: center;
  }
  
  .image-placeholder, .text-content {
    flex: 1;
  }
  
  .teachers-content {
    flex-direction: row;
    align-items: center;
  }
  
  .teacher-image, .teacher-desc {
    flex: 1;
  }
  
  .space-gallery {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 动画关键帧 */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 页面进入时的初始动画 */
.intro-container {
  animation: fadeIn 1s ease-out forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 添加一些额外的动画效果 */
.page-title {
  position: relative;
  display: inline-block;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #409EFF, transparent);
  animation: shimmer 3s infinite linear;
  background-size: 200% 100%;
}

.dot {
  animation: pulse 2s infinite;
}

.image-placeholder {
  position: relative;
  overflow: hidden;
}

.image-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: skewX(-25deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  20% { left: 100%; }
  100% { left: 100%; }
}

/* 3D 翻转效果 */
.mission-card {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.mission-card:hover .mission-title {
  animation: wiggle 0.5s ease;
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0); }
  25% { transform: rotate(3deg); }
  75% { transform: rotate(-3deg); }
}

/* 浮动效果 */
.stage-card {
  animation: float 6s ease-in-out infinite;
  animation-delay: calc(var(--delay) * 1s);
}

/* 交错动画延迟 */
.stage-card:nth-child(1) { --delay: 0; }
.stage-card:nth-child(2) { --delay: 1; }
.stage-card:nth-child(3) { --delay: 2; }

/* 图片悬停效果 */
.gallery-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover::after {
  opacity: 1;
}
/* 教学特色卡片 */
.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  transform: perspective(1000px);
  transform-style: preserve-3d;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.feature-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.feature-card:hover::after {
  transform: scaleX(1);
}

.feature-content {
  padding: 1.5rem;
  flex: 1;
}

.feature-title {
  color: #409EFF;
  margin-bottom: 0.8rem;
  font-size: 18px;
  font-weight: 600;
}

.feature-image {
  width: 100%;
  border-radius: 0 0 12px 12px;
  overflow: hidden;
}

.feature-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.5s ease;
}

.feature-card:hover .feature-image img {
  transform: scale(1.05);
}

/* 师资统计 */
.teacher-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.stat-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

/* 联系我们 */
.contact-card {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
}

.contact-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.contact-card:hover::after {
  transform: scaleX(1);
}

.contact-image {
  width: 100%;
  padding: 1.5rem;
  background-color: #f8f9fa;
  text-align: center;
}

.contact-image img {
  max-width: 200px;
  margin: 0 auto;
}

.contact-info {
  padding: 1.5rem;
  text-align: center;
}

.contact-text {
  margin-bottom: 0.8rem;
  color: #555;
}

.contact-phone {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 18px;
  font-weight: 500;
  color: #409EFF;
}

/* 响应式调整 */
@media (min-width: 992px) {
  .feature-card {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  
  .feature-content {
    width: 60%;
    padding: 2rem;
  }
  
  .feature-image {
    width: 40%;
    border-radius: 0;
    height: 100%;
  }
  
  .feature-image img {
    height: 100%;
    object-fit: cover;
  }
  
  .contact-card {
    flex-direction: row;
    align-items: center;
  }
  
  .contact-image {
    width: 40%;
  }
  
  .contact-info {
    width: 60%;
    text-align: left;
  }
  
  .contact-phone {
    justify-content: flex-start;
  }
}
</style>
