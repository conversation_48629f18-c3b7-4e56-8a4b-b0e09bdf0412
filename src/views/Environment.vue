<template>
  <div class="environment-container">
    <h2 class="page-title animate__animated animate__fadeInDown">园所环境</h2>
    
    <div class="intro-section animate-scroll">
      <p class="chinese-text">园所以蒙台梭利"有准备的环境"理论为指导，打造4000㎡智慧教学综合体；</p>
      <ul class="space-features">
        <li>主题学习场地：蒙台梭利活动室、安科纳语言馆、科学实验馆、绘本阅读馆；</li>
        <li>素质发展空间：多功能运动馆、创意美劳馆、厨艺吧、多功能表演厅；</li>
        <li>自然探索生态：2000㎡户外运动区涵盖体适能训练动线、足球场、篮球场、攀岩区、大型游乐设备、生态种植园。</li>
      </ul>
    </div>

    <div class="swipe-container animate-scroll">
      <van-swipe class="gallery" :autoplay="3000" indicator-color="#409EFF">
        <van-swipe-item v-for="img in mainImages.slice(0,3)" :key="img">
          <img :src="img" class="swipe-image" />
        </van-swipe-item>
      </van-swipe>
    </div>
     <p class="chinese-text">步入启稚，4000m²的现代化教学楼明亮通透，每一间专业蒙氏教室都精心设计，遵循幼儿发展轨迹，配备专业认证教具，营造出宁静、有序、激发内在潜能的学习氛围。我们坚持“安全为基，环保至上”，室内所有教具，均精选100%天然全实木打造。孩子们可以安心触摸、操作，在纯净的环境中自由探索。</p>
    <div class="swipe-container animate-scroll">
      <van-swipe class="gallery" :autoplay="3500" indicator-color="#67C23A">
        <van-swipe-item v-for="img in classroomImages" :key="img">
          <img :src="img" class="swipe-image" />
        </van-swipe-item>
      </van-swipe>
    </div>
     <p class="chinese-text">而更令人向往的是那超2000m²的户外操场。这里，阳光洒满开阔的操场，绿意环绕。孩子们奔跑在专业的体适能训练区，强健体魄；在迷你足球场、篮球场里面初试身手，感受团队协作的魅力；在充满挑战的攀爬区里锻炼勇气与协调；在大型游乐设备里则满载孩子们的欢声笑语，释放无限童真。</p>
    <div class="swipe-container animate-scroll">
      <van-swipe class="gallery" :autoplay="4000" indicator-color="#E6A23C">
        <van-swipe-item v-for="img in outdoorImages" :key="img">
          <img :src="img" class="swipe-image" />
        </van-swipe-item>
      </van-swipe>
    </div>
     <p class="chinese-text">我们倾心打造了一间独一无二的专业儿童厨艺吧——这里不仅是烹饪空间，更是激发感官、连接生活的奇妙课堂。我们配备了完全适合儿童尺寸的专业烹饪厨具（安全刀具、迷你灶台、烘焙工具等），孩子们得以安全地进行真实的洗、切、烹、调。每一次动手都是学习，每一缕香气都是成长。让孩子在真实的烟火气中，亲手烹饪出童年的幸福滋味与独立生活的第一课！</p>
    <div class="swipe-container animate-scroll">
      <van-swipe class="gallery" :autoplay="4000" indicator-color="#E6A23C">
        <van-swipe-item v-for="img in kitchenImages" :key="img">
          <img :src="img" class="swipe-image" />
        </van-swipe-item>
      </van-swipe>
    </div>
     <p class="chinese-text">在启稚幼儿园，宝贝的每一次自信绽放都值得喝彩！为此，我们倾力打造了一间媲美专业剧场标准的多功能表演厅，为孩子们的成长故事提供闪耀的舞台。</p>
    <div class="swipe-container animate-scroll">
      <van-swipe class="gallery" :autoplay="3500" indicator-color="#409EFF">
        <van-swipe-item v-for="img in theaterImages" :key="img">
          <img :src="img" class="swipe-image" />
        </van-swipe-item>
      </van-swipe>
    </div>
     <p class="chinese-text">为了更全面促进低龄段幼儿探索，精心打造全软包地面且拥有3套大型木质攀爬设备，可为6个月至3岁幼儿提供大运动、空间感、平衡、力量等锻炼。让幼儿的运动更科学，让环境更安全。不错过每个孩子的成长时光。</p>
    <div class="swipe-container animate-scroll">
      <van-swipe class="gallery" :autoplay="3500" indicator-color="#67C23A">
        <van-swipe-item v-for="img in playgroundImages" :key="img">
          <img :src="img" class="swipe-image" />
        </van-swipe-item>
      </van-swipe>
    </div>
     <p class="chinese-text">生态种植园，孩子们亲手播种、照料、收获，在与泥土和生命的亲密接触中，感悟自然奥秘，培养责任感与敬畏之心。</p>
    <div class="swipe-container animate-scroll">
      <van-swipe class="gallery" :autoplay="3500" indicator-color="#E6A23C">
        <van-swipe-item v-for="img in gardenImages" :key="img">
          <img :src="img" class="swipe-image" />
        </van-swipe-item>
      </van-swipe>
    </div>
     <p class="chinese-text">选择启稚，就是为孩子选择一个在阳光、绿意与无限可能中自由呼吸、茁壮成长的起点。在这里，每一个角落都诉说着对童年的尊重与珍视，等待着小小探险家们书写属于自己的精彩故事。</p>
    <div class="vertical-swipe-container">
      <van-swipe :autoplay="4000" indicator-color="#409EFF" style="border-radius: 12px;">
        <van-swipe-item v-for="img in campusImages" :key="img">
          <div class="vertical-image-wrapper">
            <img :src="img" class="vertical-image" />
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>
    <!-- /TODO -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Swipe, SwipeItem } from 'vant';
import 'animate.css';

const mainImages = ref([
  '/imgs/enviroment/22.jpg',
  '/imgs/enviroment/23.jpg',
  '/imgs/enviroment/24.jpg'
]);

const classroomImages = ref([
  '/imgs/enviroment/25.jpg',
  '/imgs/enviroment/26.jpg',
  '/imgs/enviroment/27.jpg',
  '/imgs/enviroment/28.jpg',
  '/imgs/enviroment/29.jpg'
]);

const outdoorImages = ref([
  '/imgs/enviroment/30.jpg',
  '/imgs/enviroment/31.jpg',
  '/imgs/enviroment/32.jpg',
  '/imgs/enviroment/33.jpg',
  '/imgs/enviroment/34.jpg',
  '/imgs/enviroment/35.jpg',
  '/imgs/enviroment/36.jpg',
  '/imgs/enviroment/37.jpg'
]);

const kitchenImages = ref([
  '/imgs/enviroment/38.jpg',
  '/imgs/enviroment/39.jpg',
  '/imgs/enviroment/40.jpg',
  '/imgs/enviroment/41.jpg'
]);

const theaterImages = ref([
  '/imgs/enviroment/42.jpg',
  '/imgs/enviroment/43.jpg'
]);

const playgroundImages = ref([
  '/imgs/enviroment/44.jpg',
  '/imgs/enviroment/45.jpg'
]);

const gardenImages = ref([
  '/imgs/enviroment/46.jpg',
  '/imgs/enviroment/47.jpg'
]);

const campusImages = ref([
  '/imgs/enviroment/48.jpg',
  '/imgs/enviroment/49.jpg',
  '/imgs/enviroment/50.jpg',
  '/imgs/enviroment/51.jpg'
]);

const outdoorCaptions = {
  '30.jpg': '阳光操场全景',
  '31.jpg': '体适能训练区',
  '32.jpg': '迷你足球场',
  '33.jpg': '篮球场活动',
  '34.jpg': '攀岩区挑战',
  '35.jpg': '游乐设施区',
  '36.jpg': '生态种植园',
  '37.jpg': '户外活动空间'
};

const getOutdoorCaption = (imgPath) => {
  const imgName = imgPath.split('/').pop();
  return outdoorCaptions[imgName] || '户外活动区域';
};

// 视差滚动效果
const setupParallax = () => {
  const elements = document.querySelectorAll('.animate-scroll');
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate__animated', 'animate__fadeInUp');
      }
    });
  }, { threshold: 0.1 });

  elements.forEach(el => observer.observe(el));
};

onMounted(() => {
  setupParallax();
});
</script>

<style scoped>
.environment-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.animate-scroll {
  opacity: 0;
  transition: opacity 0.6s, transform 0.6s;
}

.intro-section {
  margin-bottom: 30px;
}

.space-features {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.space-features li {
  padding: 8px 0;
  position: relative;
  padding-left: 20px;
}

.space-features li:before {
  content: "•";
  color: #409EFF;
  position: absolute;
  left: 0;
}

.swipe-container {
  margin: 30px 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.swipe-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.swipe-container {
  margin: 40px 0;
  height: 400px;
}

.van-swipe {
  height: 100%;
}

.van-swipe-item {
  height: 100%;
}

.vertical-swipe-container {
  width: 100%;
  margin: 20px 0;
}

.vertical-swipe-container .van-swipe-item {
  width: 100%;
  max-width: 720px;
  margin: 0 auto;
}

.vertical-image-wrapper {
  position: relative;
  padding-bottom: 150%; /* 1080/720=1.5 */
  height: 0;
  overflow: hidden;
}

.vertical-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}


.chinese-text {
  text-indent: 2em;
  line-height: 1.8;
  margin-bottom: 1em;
}

@media (max-width: 768px) {
  .swipe-container {
    height: 250px;
    margin: 25px 0;
  }
}

.swipe-card {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
}


@media (max-width: 768px) {
  .swipe-image {
    height: 250px;
  }
  
  .swipe-container {
    margin: 25px 0;
    border-radius: 8px;
  }

  .swipe-caption {
    padding: 10px;
    font-size: 1rem;
  }
}

.swipe-image:hover {
  transform: scale(1.03);
}

.page-title {
  text-align: center;
  margin-bottom: 2rem;
  color: #409EFF;
  position: relative;
}

.page-title:after {
  content: '';
  position: absolute;
  width: 60px;
  height: 3px;
  background-color: #409EFF;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.environment-description {
  text-align: center;
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.gallery-section {
  margin-bottom: 3rem;
}

.gallery-section h3 {
  color: #409EFF;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #EBEEF5;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.gallery-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
}

.gallery-item img {
  width: 100%;
  height: 220px;
  object-fit: cover;
  display: block;
}

.image-caption {
  padding: 1rem;
  text-align: center;
  background-color: white;
  color: #606266;
}

@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: 1fr;
  }
}
</style>
